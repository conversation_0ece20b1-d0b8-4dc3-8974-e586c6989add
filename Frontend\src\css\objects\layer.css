.cs-layer {
    display: none;
    position: fixed;
    text-align: center;
    align-content: center;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 95;
    padding: 0%;
    margin: 0%;
}

.cs-layer__icon {
    aspect-ratio: 1;
}

.cs-layer__icon--small {
    width: 2rem;
}

.cs-layer__icon--medium {
    width: 5rem;
}

.cs-layer__icon--large {
    width: 7rem;
}

.cs-layer--default {
    background: var(--color-dark-neutral-25);
}

.cs-layer--light {
    background: var(--color-light-neutral-25);
}