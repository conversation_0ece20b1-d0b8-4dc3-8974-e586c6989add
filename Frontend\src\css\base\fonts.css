/* Thin */
@font-face {
    font-family: 'Poppins';
    src: url('../../../static/fonts/Poppins/Poppins-Thin.ttf') format('truetype');
    font-weight: 100;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Poppins';
    src: url('../../../static/fonts/Poppins/Poppins-ThinItalic.ttf') format('truetype');
    font-weight: 100;
    font-style: italic;
    font-display: swap;
}

/* Extra Light */
@font-face {
    font-family: 'Poppins';
    src: url('../../../static/fonts/Poppins/Poppins-ExtraLight.ttf') format('truetype');
    font-weight: 200;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Poppins';
    src: url('../../../static/fonts/Poppins/Poppins-ExtraLightItalic.ttf') format('truetype');
    font-weight: 200;
    font-style: italic;
    font-display: swap;
}

/* Light */
@font-face {
    font-family: 'Poppins';
    src: url('../../../static/fonts/Poppins/Poppins-Light.ttf') format('truetype');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Poppins';
    src: url('../../../static/fonts/Poppins/Poppins-LightItalic.ttf') format('truetype');
    font-weight: 300;
    font-style: italic;
    font-display: swap;
}

/* Regular */
@font-face {
    font-family: 'Poppins';
    src: url('../../../static/fonts/Poppins/Poppins-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Poppins';
    src: url('../../../static/fonts/Poppins/Poppins-Italic.ttf') format('truetype');
    font-weight: normal;
    font-style: italic;
    font-display: swap;
}

/* Medium */
@font-face {
    font-family: 'Poppins';
    src: url('../../../static/fonts/Poppins/Poppins-Medium.ttf') format('truetype');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Poppins';
    src: url('../../../static/fonts/Poppins/Poppins-MediumItalic.ttf') format('truetype');
    font-weight: 500;
    font-style: italic;
    font-display: swap;
}

/* Semi Bold */
@font-face {
    font-family: 'Poppins';
    src: url('../../../static/fonts/Poppins/Poppins-SemiBold.ttf') format('truetype');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Poppins';
    src: url('../../../static/fonts/Poppins/Poppins-SemiBoldItalic.ttf') format('truetype');
    font-weight: 600;
    font-style: italic;
    font-display: swap;
}

/* Bold */
@font-face {
    font-family: 'Poppins';
    src: url('../../../static/fonts/Poppins/Poppins-Bold.ttf') format('truetype');
    font-weight: bold;
    /* 700 */
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Poppins';
    src: url('../../../static/fonts/Poppins/Poppins-BoldItalic.ttf') format('truetype');
    font-weight: bold;
    /* 700 */
    font-style: italic;
    font-display: swap;
}

/* Extra Bold */
@font-face {
    font-family: 'Poppins';
    src: url('../../../static/fonts/Poppins/Poppins-ExtraBold.ttf') format('truetype');
    font-weight: 800;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Poppins';
    src: url('../../../static/fonts/Poppins/Poppins-ExtraBoldItalic.ttf') format('truetype');
    font-weight: 800;
    font-style: italic;
    font-display: swap;
}

/* Black */
@font-face {
    font-family: 'Poppins';
    src: url('../../../static/fonts/Poppins/Poppins-Black.ttf') format('truetype');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Poppins';
    src: url('../../../static/fonts/Poppins/Poppins-BlackItalic.ttf') format('truetype');
    font-weight: 900;
    font-style: italic;
    font-display: swap;
}